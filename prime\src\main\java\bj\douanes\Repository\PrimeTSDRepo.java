package bj.douanes.Repository;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import bj.douanes.Model.PrimeTSD;

@Repository
public interface PrimeTSDRepo extends CrudRepository<PrimeTSD, Long> {

    //une requette pour recuperer la prime TSD par repartition
    @Query("SELECT * FROM primetsd WHERE id_repartition = :idRepartition")
    PrimeTSD findByIdRepartition(@Param("idRepartition") Long idRepartition);
}
