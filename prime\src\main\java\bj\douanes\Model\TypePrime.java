package bj.douanes.Model;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonInclude;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "typeprime")
public class TypePrime {

    @Id
    @Column("code_type_prime")
    private String codeTypePrime;

    @Column("libelle")
    private String libelle;

    @Column("description")
    private String description;

    @Column("periodicite")
    private String periodicite;

    @Column("nom_table_prime")
    private String nomTablePrime;

    @Column("created_at")
    private LocalDate createdAt;

    @Column("updated_at")
    private LocalDate updatedAt;

    @Column("avec_unite")
    private Boolean avecUnite;
}
