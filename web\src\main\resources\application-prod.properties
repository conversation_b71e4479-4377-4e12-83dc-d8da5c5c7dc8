# server.port=80

# app.datasource.type.names=primary,oracle

# #Primary DB 
# app.datasource.primary.url=*******************************************************************************
# app.datasource.primary.username=root
# app.datasource.primary.password=
# app.datasource.primary.driver-class-name=com.mysql.jdbc.Driver
# app.datasource.primary.flybase-name=app

# #Oracle DB
# app.datasource.oracle.url=jdbc:oracle:thin:@//*********:1521/ASY_DB?oracle.jdbc.timezoneAsRegion=false
# app.datasource.oracle.username=GDEPOT
# app.datasource.oracle.password=stat_espace
# app.datasource.oracle.driver-class-name=oracle.jdbc.OracleDriver


# app.jwt.secret-key=9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED
# app.jwt.delay=24

server.port=8081

app.datasource.type.names=secondary

#Primary DB 
app.datasource.primary.url=*****************************************************************************
app.datasource.primary.username=root
app.datasource.primary.password=
app.datasource.primary.driver-class-name=com.mysql.jdbc.Driver
app.datasource.primary.flybase-name=app

spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect

# app.datasource.primary.url=jdbc:h2:file:${pwd}/db/dbuser
# app.datasource.primary.username=admin
# app.datasource.primary.password=nimda
# app.datasource.primary.driver-class-name=org.h2.Driver
# app.datasource.primary.flybase-name=app


# #Secondary DB
#postgres database configuration
app.datasource.secondary.url=***************************************
app.datasource.secondary.username=root
app.datasource.secondary.password=rootoor
app.datasource.secondary.driver-class-name=org.postgresql.Driver
app.datasource.secondary.flybase-name=
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect


#Oracle DB
app.datasource.oracle.url=jdbc:oracle:thin:@//*********:1521/ASY_DB?oracle.jdbc.timezoneAsRegion=false
app.datasource.oracle.username=GDEPOT
app.datasource.oracle.password=stat_espace
app.datasource.oracle.driver-class-name=oracle.jdbc.OracleDriver


app.jwt.secret-key=9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D
app.jwt.delay=24





# # Configuration pour recevoir le Mail dans la boite professionnelle Mail.finances.bj
# spring.mail.host=mail.finances.bj
# spring.mail.port=587
# spring.mail.username=noreply
# spring.mail.password=BJDgD2022_
# spring.mail.properties.mail.smtp.auth=true
# spring.mail.properties.mail.smtp.starttls.enable=true

# # Configuration pour recevoir le mail sur Gmail
# spring.mail.host=smtp.gmail.com
# spring.mail.port=587
# spring.mail.username=<EMAIL>
# spring.mail.password=esmkhvpqulbncwzs
# spring.mail.properties.mail.smtp.auth=true
# spring.mail.properties.mail.smtp.starttls.enable=true

# # Configuration pour Mailtrap
# spring.mail.host=sandbox.smtp.mailtrap.io
# spring.mail.port=587
# spring.mail.username=f6fa66edc88fe4
# spring.mail.password=ade5c6d667fd9e
# spring.mail.properties.mail.smtp.auth=plain
# spring.mail.properties.mail.smtp.starttls.enable=true