<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>bj.douanes</groupId>
		<artifactId>backend</artifactId>
		<version>2.0</version>
	</parent>

	<artifactId>facade</artifactId>
	<version>${facade.version}</version>
	<name>Facade</name>
	<description>Facade project</description>

	<dependencies>
		<dependency>
			<groupId>bj.douanes</groupId>
			<artifactId>core</artifactId>
		</dependency>
		<dependency>
			<groupId>bj.douanes</groupId>
			<artifactId>sydoniaplusplus</artifactId>
		</dependency>
		<!-- <dependency>
			<groupId>bj.douanes</groupId>
			<artifactId>personal</artifactId>
		</dependency> -->
		<!-- <dependency>
			<groupId>bj.douanes</groupId>
			<artifactId>transport</artifactId>
		</dependency> -->
		<dependency>
			<groupId>bj.douanes</groupId>
			<artifactId>prime</artifactId>
		</dependency>
	</dependencies>

</project>
