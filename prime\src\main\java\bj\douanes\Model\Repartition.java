package bj.douanes.Model;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "repartition")
public class Repartition {

    @Id
    @Column("id_repartition")
    private Long idRepartition;

    @Column("code_type_prime")
    private String codeTypePrime;

    @Column("id_prime")
    private Long idPrime;

    @Column("periode")
    private String periode;

    @Column("annee")
    private String annee;

    @Column("created_at")
    private LocalDate createdAt;

    @Column("updated_at")
    private LocalDate updatedAt;

    // Relations gérées séparément dans Spring Data JDBC
    @JsonIgnore
    private transient PrimeTSD recapPrimeTSD;

    @JsonIgnore
    private transient TypePrime typePrime;
}