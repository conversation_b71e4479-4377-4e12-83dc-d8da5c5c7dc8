package bj.douanes.Model;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "r_agent_unite")
public class RAgentUnite {

    // Dans Spring Data JDBC, nous utilisons les colonnes directement
    @Column("matricule")
    private String matricule;

    @Column("code_unite")
    private String codeUnite;

    @Column("date_fin")
    private LocalDate dateFin;

    @Column("created_at")
    private LocalDate createdAt;

    @Column("updated_at")
    private LocalDate updatedAt;

    // Relations chargées séparément
    @JsonIgnore
    private transient AgentTSD agent;

    @JsonIgnore
    private transient Unite unite;
}
