package bj.douanes.core.config.flyway;

import java.util.Objects;

import org.flywaydb.core.Flyway;
import org.springframework.context.annotation.Configuration;

import bj.douanes.core.config.database.DataSourceType;
import bj.douanes.core.config.database.DatabaseComponent;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class MigrationConfig {
    
    private final DataSourceType dataSourceType;
    private final DatabaseComponent dbComponent;

    @PostConstruct
    public void migrations() {

        for (String sourceType : dataSourceType.getEnvs()) {
            String flybaseName = dbComponent.getEnv(sourceType, "flybase-name");
            boolean mustMigrate = Objects.nonNull(flybaseName) && !flybaseName.isBlank();
            if (mustMigrate) {
                //   // For  recent flyway version
                //   Flyway.configure()
                //     .dataSource(sourceType.getUrl(), sourceType.getUsername(), sourceType.getPassword())
                //     .locations(sourceType.getFlybasePath())
                //     .load()
                //     .migrate();
                Flyway flyway = new Flyway();
                flyway.setDataSource(
                    dbComponent.getEnv(sourceType, "url"),
                    dbComponent.getEnv(sourceType, "username"),
                    dbComponent.getEnv(sourceType, "password")
                );
                flyway.setLocations(String.format("classpath:db/migration/%s", flybaseName));
                //Start By chadli
                flyway.baseline(); //Exécute le baseline pour créer flyway_schema_history s'il n'est pas présent vu que j'ai changer de db
                //End CKZ
                flyway.migrate();
            }
        }
    }
}
