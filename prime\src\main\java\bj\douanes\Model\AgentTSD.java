package bj.douanes.Model;

import java.time.LocalDate;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Table(name = "agent")
public class AgentTSD {

    @Id
    @Column("matricule")
    private String matricule;

    private String nom;
    private String prenoms;
    private String sexe;

    @Column("date_nais")
    private LocalDate dateNais;

    private String ifu;
    private String telephone;
    private String email;
    private Boolean actif;

    @Column("created_at")
    private LocalDate createdAt;

    @Column("updated_at")
    private LocalDate updatedAt;

    // Dans Spring Data JDBC, les relations sont chargées séparément
    @JsonIgnore
    private transient Set<RAgentUnite> unites;

    @JsonIgnore
    private transient Set<RAgentFonction> fonctions;

    @JsonIgnore
    private transient Set<RAgentBanque> banques;
}
