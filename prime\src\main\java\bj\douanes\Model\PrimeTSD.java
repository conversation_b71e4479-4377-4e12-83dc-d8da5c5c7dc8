package bj.douanes.Model;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "primetsd")
public class PrimeTSD {

    @Id
    @Column("id_prime")
    private Long idPrime;

    @Column("oeuvre_social_global")
    private BigDecimal oeuvreSocialGlobal = BigDecimal.ZERO;

    @Column("part_global")
    private BigDecimal partGlobal = BigDecimal.ZERO;

    @Column("cumul_coef_global")
    private BigDecimal cumulCoefGlobal = BigDecimal.ZERO;

    @Column("created_at")
    private LocalDate createdAt;

    @Column("updated_at")
    private LocalDate updatedAt;

    // Référence vers la répartition - sera chargée séparément si nécessaire
    @Column("id_repartition")
    private Long idRepartition;

    @JsonIgnore
    private transient Repartition repartition;
}
