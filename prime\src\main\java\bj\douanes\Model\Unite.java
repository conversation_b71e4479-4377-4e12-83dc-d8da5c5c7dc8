package bj.douanes.Model;

import java.time.LocalDate;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "unite")
public class Unite {

    @Id
    @Column("code_unite")
    private String codeUnite;

    private String libelle;

    @Column("created_at")
    private LocalDate createdAt;

    @Column("updated_at")
    private LocalDate updatedAt;

    // Dans Spring Data JDBC, les relations sont gérées différemment
    // Cette liste sera chargée via des requêtes séparées si nécessaire
    @JsonIgnore
    private transient List<RUniteRepartition> repartitions;
}
