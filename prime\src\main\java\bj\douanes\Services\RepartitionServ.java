package bj.douanes.Services;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.Model.AgentTSD;
import bj.douanes.Model.RAgentRepartition;
import bj.douanes.Model.RAgentRepartitionId;
import bj.douanes.Model.Repartition;
import bj.douanes.Repository.AgentRepository;
import bj.douanes.Repository.RAgentRepartitionRepository;
import bj.douanes.Repository.RepartitionRepo;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

public interface RepartitionServ {
    Repartition createRepartition(Repartition repartition);
    Repartition getRepartitionById(Long id);
    List<Repartition> getAllRepartitions();
    Repartition updateRepartition(Long id, Repartition repartition);
    void deleteRepartition(Long id);
}

@RequiredArgsConstructor
@Service
class InnerRepartitionServ implements RepartitionServ {
    
    private final RepartitionRepo repartitionRepo;
    private final AgentRepository agentRepository;
    private final RAgentRepartitionRepository rAgentRepartitionRepository;

    // @Transactional
    // @Override
    // public Repartition createRepartition(Repartition repartition) {
    //     Repartition saved = repartitionRepo.save(repartition);

    //     List<AgentTSD> agents = agentRepository.findAllByActifTrue();
    //     for (AgentTSD agent : agents) {
    //         RAgentRepartition link = new RAgentRepartition();
    //         RAgentRepartitionId id = new RAgentRepartitionId();
    //         id.setMatricule(agent.getMatricule());
    //         id.setIdRepartition(saved.getIdRepartition());
    //         link.setId(id);

    //         link.setRepartition(saved); // ← essentiel ici
    //         link.setAgent(agent);

    //         link.setCreatedAt(LocalDate.now());
    //         link.setUpdatedAt(LocalDate.now());

    //         rAgentRepartitionRepository.save(link);
    //     }

    //     return saved;
    // }

    @Transactional
    @Override
    public Repartition createRepartition(Repartition repartition) {
        Repartition saved = repartitionRepo.save(repartition);

        List<AgentTSD> agents = agentRepository.findAllByActifTrue();
        List<RAgentRepartition> links = new ArrayList<>();

        for (AgentTSD agent : agents) {
            RAgentRepartitionId id = new RAgentRepartitionId();
            id.setMatricule(agent.getMatricule());
            id.setIdRepartition(saved.getIdRepartition());

            RAgentRepartition link = new RAgentRepartition();
            link.setId(id);
            link.setRepartition(saved);
            link.setAgent(agent);
            link.setCreatedAt(LocalDate.now());
            link.setUpdatedAt(LocalDate.now());

            links.add(link);
        }

        //insertion en une seule opération
        rAgentRepartitionRepository.saveAll(links);

        return saved;
    }


    @Override
    public Repartition getRepartitionById(Long id) {
        return repartitionRepo.findById(id).orElse(null);
    }

    @Override
    public List<Repartition> getAllRepartitions() {
        List<Repartition> repartitions = new ArrayList<>();
        repartitionRepo.findAll().forEach(repartitions::add);
        return repartitions;
    }

    @Override
    public Repartition updateRepartition(Long id, Repartition repartition) {
        Repartition existingRepartition = repartitionRepo.findById(id).orElse(null);
        if (existingRepartition == null) {
            return null;
        }
        existingRepartition.setCodeTypePrime(repartition.getCodeTypePrime());
        //existingRepartition.setIdPrime(repartition.getIdPrime());
        existingRepartition.setPeriode(repartition.getPeriode());
        existingRepartition.setAnnee(repartition.getAnnee());
        existingRepartition.setCreatedAt(repartition.getCreatedAt());
        existingRepartition.setUpdatedAt(repartition.getUpdatedAt());
        return repartitionRepo.save(existingRepartition);
    }

    @Override
    public void deleteRepartition(Long id) {
        repartitionRepo.deleteById(id);
    }
}
